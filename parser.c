/* * @copyright (c) 2008, <PERSON><PERSON><PERSON>, Hanoi University of Technology
 * <AUTHOR>
 * @version 1.0
 */
#include <stdio.h>
#include <stdlib.h>

#include "reader.h"
#include "scanner.h"
#include "parser.h"
#include "semantics.h"
#include "error.h"
#include "debug.h"
#include "charcode.h"

Token *currentToken;
Token *lookAhead;

extern Type* intType;
extern Type* charType;
extern SymTab* symtab;

void scan(void) {
  printf("Scanning\n");
  Token* tmp = currentToken;
  currentToken = lookAhead;
  lookAhead = getValidToken();
  printf("Done get token\n");
  free(tmp);
  printf("Done scanning\n");
}

void eat(TokenType tokenType) {
  printf("Eating\n");
  if (lookAhead->tokenType == tokenType) {
    scan();
  } else missingToken(tokenType, lookAhead->lineNo, lookAhead->colNo);
  printf("Done eating\n");
}

void compileProgram(void) {
  Object* program;
  assert("Compiling program...");

  eat(KW_PROGRAM);
  eat(TK_IDENT);
  printf("eat\n");

  program = createProgramObject(currentToken->string);
  printf("Created\n");
  enterBlock(program->progAttrs->scope);
  printf("Entered\n");
  eat(SB_SEMICOLON);

  compileBlock();
  eat(SB_PERIOD);

  exitBlock();
  assert("Done program!");
}

void compileBlock(void) {
  assert("Compiling block...");
  Object* constObj;
  ConstantValue* constValue;

  if (lookAhead->tokenType == KW_CONST) {
    eat(KW_CONST);

    do {
      eat(TK_IDENT);
      
      checkFreshIdent(currentToken->string);
      constObj = createConstantObject(currentToken->string);
      
      eat(SB_EQ);
      constValue = compileConstant();
      
      constObj->constAttrs->value = constValue;
      declareObject(constObj);
      
      eat(SB_SEMICOLON);
    } while (lookAhead->tokenType == TK_IDENT);

    compileBlock2();
  }
  else compileBlock2();
  assert("Done block!");
}

void compileBlock2(void) {
  assert("Compiling block2...");
  Object* typeObj;
  Type* actualType;

  if (lookAhead->tokenType == KW_TYPE) {
    eat(KW_TYPE);

    do {
      eat(TK_IDENT);
      
      checkFreshIdent(currentToken->string);
      typeObj = createTypeObject(currentToken->string);
      
      eat(SB_EQ);
      actualType = compileType();
      
      typeObj->typeAttrs->actualType = actualType;
      declareObject(typeObj);
      
      eat(SB_SEMICOLON);
    } while (lookAhead->tokenType == TK_IDENT);

    compileBlock3();
  }
  else compileBlock3();
  assert("Done block2!");
}

void compileBlock3(void) {
  assert("Compiling block3...");
  Object* varObj;
  Type* varType;

  if (lookAhead->tokenType == KW_VAR) {
    eat(KW_VAR);

    do {
      eat(TK_IDENT);
      
      checkFreshIdent(currentToken->string);
      varObj = createVariableObject(currentToken->string);

      eat(SB_COLON);
      varType = compileType();
      
      varObj->varAttrs->type = varType;
      declareObject(varObj);
      
      eat(SB_SEMICOLON);
    } while (lookAhead->tokenType == TK_IDENT);

    compileBlock4();
  }
  else compileBlock4();
  assert("Done block3!");
}

void compileBlock4(void) {
  assert("Compiling block4...");
  compileSubDecls();
  compileBlock5();
  assert("Done block4!");
}

void compileBlock5(void) {
  assert("Compiling block5...");
  eat(KW_BEGIN);
  compileStatements();
  eat(KW_END);
  assert("Done block5!");
}

void compileSubDecls(void) {
  assert("Compiling sub declarations...");
  while ((lookAhead->tokenType == KW_FUNCTION) || (lookAhead->tokenType == KW_PROCEDURE)) {
    if (lookAhead->tokenType == KW_FUNCTION)
      compileFuncDecl();
    else compileProcDecl();
  }
  assert("Done sub declarations!");
}

void compileFuncDecl(void) {
  assert("Compiling function declaration...");
  Object* funcObj;
  Type* returnType;

  eat(KW_FUNCTION);
  eat(TK_IDENT);

  checkFreshIdent(currentToken->string);
  funcObj = createFunctionObject(currentToken->string);
  declareObject(funcObj);

  enterBlock(funcObj->funcAttrs->scope);
  
  compileParams();

  eat(SB_COLON);
  returnType = compileBasicType();
  funcObj->funcAttrs->returnType = returnType;

  eat(SB_SEMICOLON);
  compileBlock();
  eat(SB_SEMICOLON);

  exitBlock();
  assert("Done function declaration!");
}

void compileProcDecl(void) {
  assert("Compiling procedure declaration...");
  Object* procObj;

  eat(KW_PROCEDURE);
  eat(TK_IDENT);

  checkFreshIdent(currentToken->string);
  procObj = createProcedureObject(currentToken->string);
  declareObject(procObj);

  enterBlock(procObj->procAttrs->scope);

  compileParams();

  eat(SB_SEMICOLON);
  compileBlock();
  eat(SB_SEMICOLON);

  exitBlock();
  assert("Done procedure declaration!");
}

ConstantValue* compileUnsignedConstant(void) {
  assert("Compiling unsigned constant...");
  ConstantValue* constValue;
  Object* obj;

  switch (lookAhead->tokenType) {
  case TK_NUMBER:
    eat(TK_NUMBER);
    constValue = makeIntConstant(currentToken->value);
    break;
  case TK_IDENT:
    eat(TK_IDENT);

    obj = checkDeclaredConstant(currentToken->string);
    constValue = duplicateConstantValue(obj->constAttrs->value);

    break;
  case TK_CHAR:
    eat(TK_CHAR);
    constValue = makeCharConstant(currentToken->string[0]);
    break;
  default:
    error(ERR_INVALID_CONSTANT, lookAhead->lineNo, lookAhead->colNo);
    break;
  }
  assert("Done unsigned constant!");
  return constValue;
}

ConstantValue *compileConstant(void)
{
  assert("Compiling constant...");
  ConstantValue *constValue;

  switch (lookAhead->tokenType)
  {
  case SB_PLUS:
    eat(SB_PLUS);
    constValue = compileConstant2();
    // TODO Check if type of the constant is integer
    if ((constValue == NULL) || (constValue->type != TP_INT))
    {
      error(ERR_TYPE_INCONSISTENCY, currentToken->lineNo, currentToken->colNo);
    }
    break;
  case SB_MINUS:
    eat(SB_MINUS);
    constValue = compileConstant2();
    // TODO Check if type of the constant is integer
    if ((constValue == NULL) || (constValue->type != TP_INT))
    {
      error(ERR_TYPE_INCONSISTENCY, currentToken->lineNo, currentToken->colNo);
    }
    constValue->intValue = -constValue->intValue;
    break;
  case TK_CHAR:
    eat(TK_CHAR);
    constValue = makeCharConstant(currentToken->string[0]);
    break;
  default:
    constValue = compileConstant2();
    break;
  }
  assert("Done constant!");
  return constValue;
}

ConstantValue* compileConstant2(void) {
  assert("Compiling constant2...");
  ConstantValue* constValue;
  Object* obj;

  switch (lookAhead->tokenType) {
  case TK_NUMBER:
    eat(TK_NUMBER);
    constValue = makeIntConstant(currentToken->value);
    break;
  case TK_IDENT:
    eat(TK_IDENT);
    obj = checkDeclaredConstant(currentToken->string);
    constValue = duplicateConstantValue(obj->constAttrs->value);
    break;
  default:
    error(ERR_INVALID_CONSTANT, lookAhead->lineNo, lookAhead->colNo);
    break;
  }
  assert("Done constant2!");
  return constValue;
}

Type* compileType(void) {
  assert("Compiling type...");
  Type* type;
  Type* elementType;
  int arraySize;
  Object* obj;

  switch (lookAhead->tokenType) {
  case KW_INTEGER: 
    eat(KW_INTEGER);
    type =  makeIntType();
    break;
  case KW_CHAR: 
    eat(KW_CHAR); 
    type = makeCharType();
    break;
  case KW_ARRAY:
    eat(KW_ARRAY);
    eat(SB_LSEL);
    eat(TK_NUMBER);

    arraySize = currentToken->value;

    eat(SB_RSEL);
    eat(KW_OF);
    elementType = compileType();
    type = makeArrayType(arraySize, elementType);
    break;
  case TK_IDENT:
    eat(TK_IDENT);
    obj = checkDeclaredType(currentToken->string);
    type = duplicateType(obj->typeAttrs->actualType);
    break;
  default:
    error(ERR_INVALID_TYPE, lookAhead->lineNo, lookAhead->colNo);
    break;
  }
  assert("Done type!");
  return type;
}

Type* compileBasicType(void) {
  assert("Compiling basic type...");
  Type* type;

  switch (lookAhead->tokenType) {
  case KW_INTEGER: 
    eat(KW_INTEGER); 
    type = makeIntType();
    break;
  case KW_CHAR: 
    eat(KW_CHAR); 
    type = makeCharType();
    break;
  default:
    error(ERR_INVALID_BASICTYPE, lookAhead->lineNo, lookAhead->colNo);
    break;
  }
  assert("Done basic type!");
  return type;
}

void compileParams(void) {
  assert("Compiling params...");
  if (lookAhead->tokenType == SB_LPAR) {
    eat(SB_LPAR);
    compileParam();
    while (lookAhead->tokenType == SB_SEMICOLON) {
      eat(SB_SEMICOLON);
      compileParam();
    }
    eat(SB_RPAR);
  }
  assert("Done params!");
}

void compileParam(void) {
  assert("Compiling param...");
  Object* param;
  Type* type;
  enum ParamKind paramKind;

  switch (lookAhead->tokenType) {
  case TK_IDENT:
    paramKind = PARAM_VALUE;
    break;
  case KW_VAR:
    eat(KW_VAR);
    paramKind = PARAM_REFERENCE;
    break;
  default:
    error(ERR_INVALID_PARAMETER, lookAhead->lineNo, lookAhead->colNo);
    break;
  }

  eat(TK_IDENT);
  checkFreshIdent(currentToken->string);
  param = createParameterObject(currentToken->string, paramKind, symtab->currentScope->owner);
  eat(SB_COLON);
  type = compileBasicType();
  param->paramAttrs->type = type;
  declareObject(param);
  assert("Done param!");
}

void compileStatements(void) {
  assert("Compiling statements...");
  compileStatement();
  while (lookAhead->tokenType == SB_SEMICOLON) {
    eat(SB_SEMICOLON);
    compileStatement();
  }
  assert("Done statements!");
}

void compileStatement(void) {
  assert("Compiling statement...");
  switch (lookAhead->tokenType) {
  case TK_IDENT:
    compileAssignSt();
    break;
  case KW_CALL:
    compileCallSt();
    break;
  case KW_BEGIN:
    compileGroupSt();
    break;
  case KW_IF:
    compileIfSt();
    break;
  case KW_WHILE:
    compileWhileSt();
    break;
  case KW_FOR:
    compileForSt();
    break;
    // EmptySt needs to check FOLLOW tokens
  case SB_SEMICOLON:
  case KW_END:
  case KW_ELSE:
    break;
    // Error occurs
  default:
    error(ERR_INVALID_STATEMENT, lookAhead->lineNo, lookAhead->colNo);
    break;
  }
  assert("Done statement!");
}

Type *compileLValue(void)
{
  assert("Compiling lvalue...");
  // TODO: parse a lvalue (a variable, an array element, a parameter, the current function identifier)
  Object *var;
  Type *varType;

  eat(TK_IDENT);
  // check if the identifier is a function identifier, or a variable identifier, or a parameter
  var = checkDeclaredLValueIdent(currentToken->string);
  switch (var->kind)
  {
  case OBJ_VARIABLE:
    varType = compileIndexes(var->varAttrs->type);
    break;
  case OBJ_PARAMETER:
    varType = var->paramAttrs->type;
    break;
  case OBJ_FUNCTION:
    varType = var->funcAttrs->returnType;
    break;
  }

  assert("Done lvalue!");
  return varType;
}

void compileAssignSt(void) {
  assert("Compiling assignment statement...");
  // TODO: parse the assignment and check type consistency
  Type* lvalueType = compileLValue();
  eat(SB_ASSIGN);
  Type* expType = compileExpression();
  checkTypeEquality(lvalueType, expType);
  assert("Done assignment statement!");
}

void compileCallSt(void) {
  assert("Compiling call statement...");
  Object* proc;

  eat(KW_CALL);
  eat(TK_IDENT);

  proc = checkDeclaredProcedure(currentToken->string);

  compileArguments(proc->procAttrs->paramList);
  assert("Done call statement!");
}

void compileGroupSt(void) {
  assert("Compiling group statement...");
  eat(KW_BEGIN);
  compileStatements();
  eat(KW_END);
  assert("Done group statement!");
}

void compileIfSt(void) {
  assert("Compiling if statement...");
  eat(KW_IF);
  compileCondition();
  eat(KW_THEN);
  compileStatement();
  if (lookAhead->tokenType == KW_ELSE) 
    compileElseSt();
  assert("Done if statement!");
}

void compileElseSt(void) {
  assert("Compiling else statement...");
  eat(KW_ELSE);
  compileStatement();
  assert("Done else statement!");
}

void compileWhileSt(void) {
  assert("Compiling while statement...");
  eat(KW_WHILE);
  compileCondition();
  eat(KW_DO);
  compileStatement();
  assert("Done while statement!");
}

void compileForSt(void) {
  assert("Compiling for statement...");
  // TODO: Check type consistency of FOR's variable
  eat(KW_FOR);
  eat(TK_IDENT);

  // check if the identifier is a variable
  Object *var = checkDeclaredVariable(currentToken->string);
  Type *varType = var->varAttrs->type;

  eat(SB_ASSIGN);
  Type* expType1 = compileExpression();

  checkTypeEquality(varType, expType1);

  eat(KW_TO);
  Type* expType2 = compileExpression();
  checkTypeEquality(expType1, expType2);

  eat(KW_DO);
  compileStatement();
  assert("Done for statement!");
}

void compileArgument(Object *param)
{
  assert("Compiling argument...");
  // TODO: parse an argument, and check type consistency
  //       If the corresponding parameter is a reference, the argument must be a lvalue
  Type *argType;

  if (param->paramAttrs->kind == PARAM_REFERENCE)
  {
    if (lookAhead->tokenType == TK_IDENT)
    {
      checkDeclaredLValueIdent(lookAhead->string);
    }
    else
    {
      error(ERR_TYPE_INCONSISTENCY, lookAhead->lineNo, lookAhead->colNo);
    }
  }
  argType = compileExpression();
  checkTypeEquality(argType, param->paramAttrs->type);
  assert("Done argument!");
}

void compileArguments(ObjectNode *paramList)
{
  // TODO: parse a list of arguments, check the consistency of the arguments and the given parameters
  switch (lookAhead->tokenType)
  {
  case SB_LPAR:
    eat(SB_LPAR);
    if (paramList == NULL)
    {
      error(ERR_PARAMETERS_ARGUMENTS_INCONSISTENCY, currentToken->lineNo, currentToken->colNo);
    }
    compileArgument(paramList->object);
    paramList = paramList->next;

    while (lookAhead->tokenType == SB_COMMA)
    {
      eat(SB_COMMA);
      if (paramList == NULL)
      {
        error(ERR_PARAMETERS_ARGUMENTS_INCONSISTENCY, currentToken->lineNo, currentToken->colNo);
      }
      compileArgument(paramList->object);
      paramList = paramList->next;
    }
    // Check if there are more arguments than parameters
    if (paramList != NULL)
    {
      error(ERR_PARAMETERS_ARGUMENTS_INCONSISTENCY, currentToken->lineNo, currentToken->colNo);
    }

    eat(SB_RPAR);
    break;
    // Check FOLLOW set
  case SB_TIMES:
  case SB_SLASH:
  case SB_PLUS:
  case SB_MINUS:
  case KW_TO:
  case KW_DO:
  case SB_RPAR:
  case SB_COMMA:
  case SB_EQ:
  case SB_NEQ:
  case SB_LE:
  case SB_LT:
  case SB_GE:
  case SB_GT:
  case SB_RSEL:
  case SB_SEMICOLON:
  case KW_END:
  case KW_ELSE:
  case KW_THEN:
    // Check if actual parameter list is empty, but formal has parameters
    if (paramList != NULL)
    {
      error(ERR_PARAMETERS_ARGUMENTS_INCONSISTENCY, currentToken->lineNo, currentToken->colNo);
    }
    break;
  default:
    error(ERR_INVALID_ARGUMENTS, lookAhead->lineNo, lookAhead->colNo);
  }
}

void compileCondition(void) {
  assert("Compiling condition...");
  // TODO: check the type consistency of LHS and RSH, check the basic type
  Type* expType1 = compileExpression();

  switch (lookAhead->tokenType) {
  case SB_EQ:
    eat(SB_EQ);
    break;
  case SB_NEQ:
    eat(SB_NEQ);
    break;
  case SB_LE:
    eat(SB_LE);
    break;
  case SB_LT:
    eat(SB_LT);
    break;
  case SB_GE:
    eat(SB_GE);
    break;
  case SB_GT:
    eat(SB_GT);
    break;
  default:
    error(ERR_INVALID_COMPARATOR, lookAhead->lineNo, lookAhead->colNo);
  }

  Type* expType2 = compileExpression();
  checkTypeEquality(expType1, expType2);
  assert("Done condition!");
}

Type* compileExpression(void) {
  assert("Compiling expression...");
  Type* type;
  
  switch (lookAhead->tokenType) {
  case SB_PLUS:
    eat(SB_PLUS);
    type = compileExpression2();
    checkIntType(type);
    break;
  case SB_MINUS:
    eat(SB_MINUS);
    type = compileExpression2();
    checkIntType(type);
    break;
  default:
    type = compileExpression2();
  }
  assert("Done expression!");
  return type;
}

Type* compileExpression2(void) {
  assert("Compiling expression2...");
  Type* type;

  type = compileTerm();
  compileExpression3();

  assert("Done expression2!");
  return type;
}


void compileExpression3(void) {
  assert("Compiling expression3...");
  Type* type;

  switch (lookAhead->tokenType) {
  case SB_PLUS:
    eat(SB_PLUS);
    type = compileTerm();
    checkIntType(type);
    compileExpression3();
    break;
  case SB_MINUS:
    eat(SB_MINUS);
    type = compileTerm();
    checkIntType(type);
    compileExpression3();
    break;
    // check the FOLLOW set
  case KW_TO:
  case KW_DO:
  case SB_RPAR:
  case SB_COMMA:
  case SB_EQ:
  case SB_NEQ:
  case SB_LE:
  case SB_LT:
  case SB_GE:
  case SB_GT:
  case SB_RSEL:
  case SB_SEMICOLON:
  case KW_END:
  case KW_ELSE:
  case KW_THEN:
    break;
  default:
    error(ERR_INVALID_EXPRESSION, lookAhead->lineNo, lookAhead->colNo);
  }
  assert("Done expression3!");
}

Type* compileTerm(void) {
  assert("Compiling term...");
  Type* type;

  type = compileFactor();
  compileTerm2();

  assert("Done term!");
  return type;
}

void compileTerm2(void) {
  assert("Compiling term2...");
  Type* type;

  switch (lookAhead->tokenType) {
  case SB_TIMES:
    eat(SB_TIMES);
    type = compileFactor();
    checkIntType(type);
    compileTerm2();
    break;
  case SB_SLASH:
    eat(SB_SLASH);
    type = compileFactor();
    checkIntType(type);
    compileTerm2();
    break;
    // check the FOLLOW set
  case SB_PLUS:
  case SB_MINUS:
  case KW_TO:
  case KW_DO:
  case SB_RPAR:
  case SB_COMMA:
  case SB_EQ:
  case SB_NEQ:
  case SB_LE:
  case SB_LT:
  case SB_GE:
  case SB_GT:
  case SB_RSEL:
  case SB_SEMICOLON:
  case KW_END:
  case KW_ELSE:
  case KW_THEN:
    break;
  default:
    error(ERR_INVALID_TERM, lookAhead->lineNo, lookAhead->colNo);
  }
  assert("Done term2!");
}

Type* compileFactor(void) {
  assert("Compiling factor...");
  Type* type;
  Object* obj;

  switch (lookAhead->tokenType) {
  case TK_NUMBER:
    eat(TK_NUMBER);
    type = intType;
    break;
  case TK_CHAR:
    eat(TK_CHAR);
    type = charType;
    break;
  case TK_IDENT:
    eat(TK_IDENT);
    obj = checkDeclaredIdent(currentToken->string);

    switch (obj->kind) {
    case OBJ_CONSTANT:
      switch (obj->constAttrs->value->type) {
      case TP_INT:
        type = intType;
        break;
      case TP_CHAR:
        type = charType;
        break;
      default:
        break;
      }
      break;
    case OBJ_VARIABLE:
      if (obj->varAttrs->type->typeClass == TP_ARRAY)
        type = compileIndexes(obj->varAttrs->type);
      else 
        type = obj->varAttrs->type;
      break;
    case OBJ_PARAMETER:
      type = obj->paramAttrs->type;
      break;
    case OBJ_FUNCTION:
      compileArguments(obj->funcAttrs->paramList);
      type = obj->funcAttrs->returnType;
      break;
    default: 
      error(ERR_INVALID_FACTOR,currentToken->lineNo, currentToken->colNo);
      break;
    }
    break;
    case SB_LPAR:
    eat(SB_LPAR);
    type = compileExpression();
    eat(SB_RPAR);
    break;
  default:
    error(ERR_INVALID_FACTOR, lookAhead->lineNo, lookAhead->colNo);
  }
  
  assert("Done factor!");
  return type;
}

Type* compileIndexes(Type* arrayType) {
  assert("Compiling indexes...");
  Type* type;

  while (lookAhead->tokenType == SB_LSEL) {
    eat(SB_LSEL);
    type = compileExpression();
    checkIntType(type);
    checkArrayType(arrayType);
    arrayType = arrayType->elementType;
    eat(SB_RSEL);
  }
  checkBasicType(arrayType);
  assert("Done indexes!");
  return arrayType;
}

int compile(char *fileName) {
  assert("Compiling...");
  if (openInputStream(fileName) == IO_ERROR)
    return IO_ERROR;

  currentToken = NULL;
  lookAhead = getValidToken();

  initSymTab();
  compileProgram();
  printf("compiled\n");
  printObject(symtab->program,0);

  cleanSymTab();

  free(currentToken);
  free(lookAhead);
  closeInputStream();
  assert("Done compilation!");
  return IO_SUCCESS;

}